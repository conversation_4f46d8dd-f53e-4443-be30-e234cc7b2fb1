#!/bin/bash

# Production Deployment Script for Home Services Authentication API
# Usage: ./deploy.sh [environment]
# Example: ./deploy.sh production

set -e  # Exit on any error

ENVIRONMENT=${1:-production}
PROJECT_DIR="/home/<USER>/django_backend"
VENV_DIR="$PROJECT_DIR/venv"
SERVICE_NAME="home-services-api"

echo "🚀 Starting deployment for $ENVIRONMENT environment..."

# Check if running as correct user
if [ "$USER" != "homeservices" ] && [ "$USER" != "root" ]; then
    echo "⚠️  Warning: Not running as homeservices or root user"
fi

# Navigate to project directory
cd "$PROJECT_DIR"

echo "📦 Pulling latest code..."
git pull origin main

echo "🐍 Activating virtual environment..."
source "$VENV_DIR/bin/activate"

echo "📋 Installing/updating dependencies..."
pip install -r requirements-prod.txt

echo "🔧 Running Django checks..."
python manage.py check --deploy

echo "💾 Running database migrations..."
python manage.py migrate --noinput

echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

echo "🧹 Clearing cache..."
python manage.py shell -c "from django.core.cache import cache; cache.clear()"

echo "🔄 Restarting services..."
sudo systemctl restart "$SERVICE_NAME"
sudo systemctl restart nginx

echo "⏳ Waiting for service to start..."
sleep 5

echo "🏥 Running health check..."
HEALTH_URL="http://localhost:8000/api/auth/health/"
if curl -f -s "$HEALTH_URL" > /dev/null; then
    echo "✅ Health check passed!"
else
    echo "❌ Health check failed!"
    echo "🔍 Checking service status..."
    sudo systemctl status "$SERVICE_NAME"
    exit 1
fi

echo "📊 Service status:"
sudo systemctl status "$SERVICE_NAME" --no-pager -l

echo "🎉 Deployment completed successfully!"
echo "📍 API is available at: https://your-domain.com/api/"
echo "🔧 Admin interface: https://your-domain.com/admin/"
echo "📖 API docs: https://your-domain.com/api/docs/"

# Optional: Send deployment notification
# curl -X POST "https://hooks.slack.com/your-webhook" \
#      -H 'Content-type: application/json' \
#      --data '{"text":"🚀 Home Services Auth API deployed successfully!"}'
