(venv) homeservices@vmi2555830:~/django_backend$ python manage.py check --deploy
SystemCheckError: System check identified some issues:

ERRORS:
?: (drf_spectacular.E001) Schema generation threw exception "It is redundant to specify `source='is_valid'` on field 'ReadOnlyField' in serializer 'DiscountSerializer', because it is the same as the field name. Remove the `source` keyword argument."

WARNINGS:
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileLoginView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df4f10>" for POST /api/auth/login/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileLoginView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df4f40>" for POST /api/auth/login/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileLoginView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df5090>" for POST /api/auth/login/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileRegisterView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df7d90>" for POST /api/auth/register/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileRegisterView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df7e80>" for POST /api/auth/register/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [ResendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df6770>" for POST /api/auth/otp/resend/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [ResendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df67d0>" for POST /api/auth/otp/resend/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [ResendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df6800>" for POST /api/auth/otp/resend/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df4430>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df4460>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df4490>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df44c0>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [VerifyOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df4be0>" for POST /api/auth/otp/verify/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [VerifyOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f5708df4c70>" for POST /api/auth/otp/verify/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer > CartItemSerializer]: unable to resolve type hint for function "get_savings". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer > CartItemSerializer]: unable to resolve type hint for function "get_total_price". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_cgst_amount". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_igst_amount". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_items_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_service_charge". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_sgst_amount". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_tax_breakdown". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_ugst_amount". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_unique_services_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartSummaryView > CartSummarySerializer]: unable to resolve type hint for function "get_items_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartSummaryView > CartSummarySerializer]: unable to resolve type hint for function "get_unique_services_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_current_price". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_discount_percentage". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_seo_description". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_seo_title". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer]: unable to resolve type hint for function "get_og_image". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer]: unable to resolve type hint for function "get_seo_description". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer]: unable to resolve type hint for function "get_seo_title". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer]: unable to resolve type hint for function "get_services_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryListCreateView > CategoryListSerializer]: unable to resolve type hint for function "get_seo_description". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryListCreateView > CategoryListSerializer]: unable to resolve type hint for function "get_seo_title". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryListCreateView > CategoryListSerializer]: unable to resolve type hint for function "get_services_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AddressDetailView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AddressListCreateView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminFailedAttemptsView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminResetRateLimitView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminUnlockAccountView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminUserStatsView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminUsersListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [ChangePasswordView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [EmailLoginView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [HealthCheckView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [LogoutView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [RegisterView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [UserProfileView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [AdminCartItemListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [AdminCartListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [add_to_cart]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [apply_coupon]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [clear_cart]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [debug_cart_info]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [remove_cart_item]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [remove_coupon]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [update_cart_item]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/catalogue/views.py: Error [category_services]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/taxation/views.py: Error [AdminGSTRateListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/taxation/views.py: Error [AdminTaxCategoryListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/taxation/views.py: Error [AdminTaxConfigurationListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (security.W004) You have not set a value for the SECURE_HSTS_SECONDS setting. If your entire site is served only over SSL, you may want to consider setting a value and enabling HTTP Strict Transport Security. Be sure to read the documentation first; enabling HSTS carelessly can cause serious, irreversible problems.
?: (security.W008) Your SECURE_SSL_REDIRECT setting is not set to True. Unless your site should be available over both SSL and non-SSL connections, you may want to either set this setting True or configure a load balancer or reverse-proxy server to redirect all connections to HTTPS.
?: (security.W012) SESSION_COOKIE_SECURE is not set to True. Using a secure-only session cookie makes it more difficult for network traffic sniffers to hijack user sessions.
?: (security.W016) You have 'django.middleware.csrf.CsrfViewMiddleware' in your MIDDLEWARE, but you have not set CSRF_COOKIE_SECURE to True. Using a secure-only CSRF cookie makes it more difficult for network traffic sniffers to steal the CSRF token.
?: (security.W018) You should not have DEBUG set to True in deployment.