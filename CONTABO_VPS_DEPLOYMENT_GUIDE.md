# 🚀 Complete Contabo VPS Deployment Guide for Home Services API

This guide will help you deploy your Django Home Services API to a Contabo VPS server with production-ready configuration.

## 📋 Prerequisites

- Contabo VPS server with Ubuntu 20.04/22.04
- Domain name (optional but recommended)
- SSH access to your server
- Basic knowledge of Linux commands

## 🎯 Deployment Overview

Your Django application uses a multi-database microservices architecture with:
- **7 PostgreSQL databases** for different services
- **Redis** for caching and sessions
- **Nginx** as reverse proxy
- **Gunicorn** as WSGI server
- **SSL certificates** for HTTPS

---

## 📦 Step 1: Server Setup & Dependencies

### 1.1 Connect to Your VPS
```bash
ssh root@your-server-ip
```

### 1.2 Update System
```bash
apt update && apt upgrade -y
```

### 1.3 Install Required Packages
```bash
# Python and development tools
apt install -y python3 python3-pip python3-venv python3-dev

# PostgreSQL
apt install -y postgresql postgresql-contrib

# Redis
apt install -y redis-server

# Nginx
apt install -y nginx

# Additional tools
apt install -y git curl wget unzip supervisor htop

# SSL certificate tools
apt install -y certbot python3-certbot-nginx

# System dependencies for Python packages
apt install -y build-essential libpq-dev libjpeg-dev libpng-dev libfreetype6-dev
```

### 1.4 Create Application User
```bash
# Create user for running the application
useradd -m -s /bin/bash homeservices
usermod -aG sudo homeservices

# Switch to application user
su - homeservices
```

---

## 🗄️ Step 2: Database Setup

### 2.1 Configure PostgreSQL
```bash
# Switch to postgres user
sudo -u postgres psql

-- Create database user
CREATE USER home_services_user WITH PASSWORD 'your-secure-password';

-- Create databases
CREATE DATABASE home_services_auth OWNER home_services_user;
CREATE DATABASE home_services_catalogue OWNER home_services_user;
CREATE DATABASE home_services_cart OWNER home_services_user;
CREATE DATABASE home_services_coupons OWNER home_services_user;
CREATE DATABASE home_services_orders OWNER home_services_user;
CREATE DATABASE home_services_payments OWNER home_services_user;
CREATE DATABASE home_services_providers OWNER home_services_user;
CREATE DATABASE home_services_scheduling OWNER home_services_user;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE home_services_auth TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_catalogue TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_cart TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_coupons TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_orders TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_payments TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_providers TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_scheduling TO home_services_user;

-- Exit PostgreSQL
\q
```

### 2.2 Configure PostgreSQL for Production
```bash
sudo nano /etc/postgresql/*/main/postgresql.conf

# Add/modify these settings:
listen_addresses = 'localhost'
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
```

```bash
sudo nano /etc/postgresql/*/main/pg_hba.conf

# Add this line for local connections:
local   all             home_services_user                      md5
```

```bash
# Restart PostgreSQL
sudo systemctl restart postgresql
sudo systemctl enable postgresql
```

---

## 🔧 Step 3: Redis Configuration

```bash
# Configure Redis
sudo nano /etc/redis/redis.conf

# Modify these settings:
bind 127.0.0.1
maxmemory 256mb
maxmemory-policy allkeys-lru

# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

---

## 📁 Step 4: Application Deployment

### 4.1 Clone Repository
```bash
# As homeservices user
cd /home/<USER>
git clone https://github.com/your-username/your-repo.git django_backend
cd django_backend
```

### 4.2 Create Virtual Environment
```bash
python3 -m venv venv
source venv/bin/activate
```

### 4.3 Install Dependencies
```bash
pip install --upgrade pip
pip install -r requirements-prod.txt
```

### 4.4 Configure Environment Variables
```bash
# Copy and edit production environment file
cp .env.production .env
nano .env
```

**Update these critical values in .env:**
```env
SECRET_KEY=your-super-secret-production-key-here-change-this
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,your-server-ip
SERVER_IP=your-server-ip-address
DOMAIN_NAME=your-domain.com

# Database settings
DB_USER=home_services_user
DB_PASSWORD=your-secure-password

# Security settings
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
CSRF_TRUSTED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# MSG91 and other API keys
MSG91_AUTH_KEY=your-msg91-auth-key
MSG91_TEMPLATE_ID=your-msg91-template-id
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
```

### 4.5 Run Database Migrations
```bash
# Test database connection
python manage.py check --deploy

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput
```

---

## 🌐 Step 5: Nginx Configuration

### 5.1 Create Nginx Configuration
```bash
sudo nano /etc/nginx/sites-available/home-services-api
```

Copy the content from your `nginx.conf` file and update:
- Replace `your-domain.com` with your actual domain
- Replace `/path/to/your/django_backend` with `/home/<USER>/django_backend`
- Update SSL certificate paths (we'll set up SSL in next step)

### 5.2 Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/home-services-api /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default
sudo nginx -t
```

---

## 🔒 Step 6: SSL Certificate Setup

### 6.1 Get SSL Certificate (Let's Encrypt)
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 6.2 Auto-renewal Setup
```bash
sudo crontab -e

# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

---

## ⚙️ Step 7: Process Management (Systemd)

### 7.1 Create Systemd Service
```bash
sudo nano /etc/systemd/system/home-services-api.service
```

Copy content from `home-services-auth.service` and update paths:
- Replace `/path/to/your/django_backend` with `/home/<USER>/django_backend`
- Update user to `homeservices`

### 7.2 Create Log Directories
```bash
sudo mkdir -p /var/log/gunicorn
sudo chown homeservices:homeservices /var/log/gunicorn
```

### 7.3 Update Gunicorn Configuration
```bash
nano /home/<USER>/django_backend/gunicorn.conf.py

# Update these paths:
user = "homeservices"
group = "homeservices"
```

### 7.4 Enable and Start Services
```bash
sudo systemctl daemon-reload
sudo systemctl enable home-services-api
sudo systemctl start home-services-api
sudo systemctl enable nginx
sudo systemctl restart nginx
```

---

## 🔥 Step 8: Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw --force enable
```

---

## 🛡️ Step 9: Security Hardening

### 9.1 Install Fail2Ban
```bash
sudo apt install -y fail2ban

sudo nano /etc/fail2ban/jail.local
```

Add this configuration:
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
```

```bash
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 9.2 Secure SSH
```bash
sudo nano /etc/ssh/sshd_config

# Modify these settings:
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
```

```bash
sudo systemctl restart ssh
```

---

## 📊 Step 10: Monitoring & Logging

### 10.1 Setup Log Rotation
```bash
sudo nano /etc/logrotate.d/home-services-api
```

```
/home/<USER>/django_backend/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 homeservices homeservices
}
```

### 10.2 Monitor Services
```bash
# Check service status
sudo systemctl status home-services-api
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status redis-server

# View logs
sudo journalctl -u home-services-api -f
tail -f /home/<USER>/django_backend/logs/django.log
```

---

## ✅ Step 11: Final Testing

### 11.1 Health Check
```bash
curl -k https://your-domain.com/api/auth/health/
```

### 11.2 API Testing
```bash
# Test registration
curl -X POST https://your-domain.com/api/auth/register/mobile/ \
  -H "Content-Type: application/json" \
  -d '{"mobile_number": "+919999999999", "user_type": "customer"}'
```

---

## 🚀 Step 12: Deployment Script

Make the deployment script executable:
```bash
chmod +x /home/<USER>/django_backend/deploy.sh

# Update paths in deploy.sh
nano /home/<USER>/django_backend/deploy.sh
# Replace PROJECT_DIR="/path/to/your/django_backend" with PROJECT_DIR="/home/<USER>/django_backend"
```

---

## 📝 Important Notes

1. **Backup Strategy**: Set up regular database backups
2. **Monitoring**: Consider using tools like Prometheus/Grafana
3. **Updates**: Always test updates in staging before production
4. **Security**: Regularly update system packages and dependencies
5. **Performance**: Monitor resource usage and optimize as needed

---

## 🆘 Troubleshooting

### Common Issues:
1. **Database Connection**: Check PostgreSQL service and credentials
2. **Static Files**: Ensure proper permissions on staticfiles directory
3. **SSL Issues**: Verify domain DNS settings
4. **Memory Issues**: Monitor RAM usage, consider upgrading VPS

### Useful Commands:
```bash
# Check service logs
sudo journalctl -u home-services-api --since "1 hour ago"

# Restart services
sudo systemctl restart home-services-api nginx

# Check database connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# Monitor system resources
htop
df -h
free -h
```

---

## 🎉 Congratulations!

Your Django Home Services API is now deployed on Contabo VPS with:
- ✅ Production-ready configuration
- ✅ SSL encryption
- ✅ Multi-database architecture
- ✅ Redis caching
- ✅ Security hardening
- ✅ Process management
- ✅ Monitoring setup

**API Endpoints:**
- Main API: `https://your-domain.com/api/`
- Admin Panel: `https://your-domain.com/admin/`
- API Documentation: `https://your-domain.com/api/docs/`
- Health Check: `https://your-domain.com/api/auth/health/`
