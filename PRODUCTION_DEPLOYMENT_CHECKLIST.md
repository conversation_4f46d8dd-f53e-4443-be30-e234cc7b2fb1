# 📋 Production Deployment Checklist for Home Services API

## ✅ Pre-Deployment Checklist

### 🔧 Code & Configuration
- [ ] **Environment Variables**: Update `.env` with production values
- [ ] **Secret Key**: Generate new SECRET_KEY for production
- [ ] **Debug Mode**: Set `DEBUG=False`
- [ ] **Allowed Hosts**: Configure proper domain/IP addresses
- [ ] **Database Credentials**: Set secure database passwords
- [ ] **API Keys**: Configure MSG91, Razorpay, and other service keys
- [ ] **CORS Settings**: Configure frontend domain origins
- [ ] **SSL Settings**: Enable HTTPS security settings

### 🗄️ Database Setup
- [ ] **PostgreSQL Installation**: Install and configure PostgreSQL
- [ ] **Database Creation**: Create all 8 databases for microservices
- [ ] **User Creation**: Create `home_services_user` with proper permissions
- [ ] **Connection Testing**: Test database connections
- [ ] **Migrations**: Run all database migrations successfully
- [ ] **Superuser**: Create Django admin superuser

### 🔒 Security Configuration
- [ ] **Firewall**: Configure UFW with proper rules
- [ ] **Fail2Ban**: Install and configure fail2ban
- [ ] **SSH Security**: Disable root login, use key authentication
- [ ] **SSL Certificates**: Install Let's Encrypt certificates
- [ ] **Security Headers**: Configure Nginx security headers
- [ ] **Rate Limiting**: Enable API rate limiting

### 🌐 Web Server Setup
- [ ] **Nginx Installation**: Install and configure Nginx
- [ ] **Site Configuration**: Create Nginx site configuration
- [ ] **SSL Configuration**: Configure HTTPS redirects
- [ ] **Static Files**: Configure static file serving
- [ ] **Proxy Settings**: Configure reverse proxy to Gunicorn

### ⚙️ Application Server
- [ ] **Gunicorn Configuration**: Configure Gunicorn settings
- [ ] **Systemd Service**: Create and enable systemd service
- [ ] **Process Management**: Configure automatic restarts
- [ ] **Log Directories**: Create and set permissions for log directories

### 📊 Monitoring & Logging
- [ ] **Log Rotation**: Configure logrotate for application logs
- [ ] **Health Checks**: Test health check endpoint
- [ ] **Service Monitoring**: Configure service status monitoring
- [ ] **Error Tracking**: Set up error monitoring (optional: Sentry)

## 🚀 Deployment Steps

### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip python3-venv postgresql redis-server nginx git
```

### 2. Application Deployment
```bash
# Clone repository
git clone your-repo-url /home/<USER>/django_backend

# Setup virtual environment
python3 -m venv /home/<USER>/django_backend/venv
source /home/<USER>/django_backend/venv/bin/activate

# Install dependencies
pip install -r requirements-prod.txt
```

### 3. Database Setup
```bash
# Create databases and user
sudo -u postgres psql
CREATE USER home_services_user WITH PASSWORD 'secure-password';
CREATE DATABASE home_services_auth OWNER home_services_user;
# ... create all 8 databases
```

### 4. Configuration Files
```bash
# Copy and configure environment file
cp .env.production .env
nano .env  # Update with production values

# Copy systemd service
sudo cp home-services-auth.service /etc/systemd/system/home-services-api.service

# Copy Nginx configuration
sudo cp nginx.conf /etc/nginx/sites-available/home-services-api
sudo ln -s /etc/nginx/sites-available/home-services-api /etc/nginx/sites-enabled/
```

### 5. SSL Certificate
```bash
# Install Let's Encrypt certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 6. Service Startup
```bash
# Enable and start services
sudo systemctl daemon-reload
sudo systemctl enable home-services-api
sudo systemctl start home-services-api
sudo systemctl restart nginx
```

## 🧪 Testing Checklist

### API Endpoints Testing
- [ ] **Health Check**: `GET /api/auth/health/`
- [ ] **Admin Panel**: `GET /admin/` (should show login page)
- [ ] **API Documentation**: `GET /api/docs/`
- [ ] **User Registration**: `POST /api/auth/register/mobile/`
- [ ] **OTP Sending**: `POST /api/auth/otp/send/`
- [ ] **User Login**: `POST /api/auth/login/mobile/`
- [ ] **Protected Endpoints**: Test with JWT authentication

### Performance Testing
- [ ] **Response Times**: Check API response times
- [ ] **Database Connections**: Monitor database connection pool
- [ ] **Memory Usage**: Check application memory consumption
- [ ] **CPU Usage**: Monitor CPU usage under load

### Security Testing
- [ ] **HTTPS Redirect**: HTTP should redirect to HTTPS
- [ ] **Security Headers**: Check security headers in response
- [ ] **Rate Limiting**: Test rate limiting on sensitive endpoints
- [ ] **Authentication**: Test JWT token validation

## 📊 Monitoring Commands

### Service Status
```bash
# Check service status
sudo systemctl status home-services-api
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status redis-server

# View logs
sudo journalctl -u home-services-api -f
tail -f /home/<USER>/django_backend/logs/django.log
```

### System Resources
```bash
# Check system resources
htop
df -h
free -h

# Check database connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# Check Redis status
redis-cli ping
```

### Network & Security
```bash
# Check open ports
sudo netstat -tlnp

# Check firewall status
sudo ufw status

# Check fail2ban status
sudo fail2ban-client status
```

## 🆘 Troubleshooting

### Common Issues
1. **Database Connection Errors**
   - Check PostgreSQL service status
   - Verify database credentials in .env
   - Check database user permissions

2. **Static Files Not Loading**
   - Run `python manage.py collectstatic`
   - Check Nginx static file configuration
   - Verify file permissions

3. **SSL Certificate Issues**
   - Check domain DNS settings
   - Verify certificate installation
   - Check Nginx SSL configuration

4. **Service Won't Start**
   - Check systemd service logs
   - Verify file paths in service file
   - Check user permissions

### Emergency Commands
```bash
# Restart all services
sudo systemctl restart home-services-api nginx postgresql redis-server

# Check all service logs
sudo journalctl -u home-services-api --since "1 hour ago"

# Emergency stop
sudo systemctl stop home-services-api

# Check configuration syntax
sudo nginx -t
python manage.py check --deploy
```

## 🎯 Post-Deployment Tasks

### Immediate (Day 1)
- [ ] **Backup Setup**: Configure database backups
- [ ] **Monitoring Setup**: Set up basic monitoring
- [ ] **Documentation**: Update API documentation
- [ ] **Team Access**: Provide access credentials to team

### Short Term (Week 1)
- [ ] **Performance Monitoring**: Set up detailed monitoring
- [ ] **Log Analysis**: Review application logs
- [ ] **Security Audit**: Perform security review
- [ ] **Load Testing**: Conduct load testing

### Long Term (Month 1)
- [ ] **Automated Backups**: Set up automated backup system
- [ ] **Monitoring Alerts**: Configure alerting system
- [ ] **Performance Optimization**: Optimize based on usage patterns
- [ ] **Security Updates**: Regular security updates

## 📞 Support Information

### Important File Locations
- **Application**: `/home/<USER>/django_backend/`
- **Logs**: `/home/<USER>/django_backend/logs/`
- **Nginx Config**: `/etc/nginx/sites-available/home-services-api`
- **Systemd Service**: `/etc/systemd/system/home-services-api.service`
- **SSL Certificates**: `/etc/letsencrypt/live/your-domain.com/`

### Key Commands
```bash
# Deploy updates
cd /home/<USER>/django_backend && ./deploy.sh

# View real-time logs
sudo journalctl -u home-services-api -f

# Restart application
sudo systemctl restart home-services-api

# Check health
curl https://your-domain.com/api/auth/health/
```

---

## ✅ Final Verification

Once all items are checked:
- [ ] **All services running**: All systemd services active
- [ ] **HTTPS working**: SSL certificate valid and working
- [ ] **API responding**: All endpoints returning expected responses
- [ ] **Database connected**: All database connections working
- [ ] **Monitoring active**: Logs being generated and rotated
- [ ] **Backups configured**: Database backup system in place
- [ ] **Documentation updated**: All documentation reflects production setup

**🎉 Deployment Complete!**

Your Django Home Services API is now live and production-ready on your Contabo VPS server.
