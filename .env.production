# Production Environment Configuration
# Copy this to .env and update with your production values

# Django Settings
SECRET_KEY=your-super-secret-production-key-here-change-this
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,your-server-ip

# Server Configuration
SERVER_IP=your-server-ip-address
DOMAIN_NAME=your-domain.com

# Database Configuration
# Option 1: Use single database (simpler for small deployments)
USE_SINGLE_DATABASE=False

# Main Database (Authentication & Core)
DB_NAME=home_services_auth
DB_USER=home_services_user
DB_PASSWORD=your-secure-database-password
DB_HOST=localhost
DB_PORT=5432

# Microservice Databases (if USE_SINGLE_DATABASE=False)
CATALOGUE_DB_NAME=home_services_catalogue
CART_DB_NAME=home_services_cart
COUPONS_DB_NAME=home_services_coupons
ORDERS_DB_NAME=home_services_orders
PAYMENTS_DB_NAME=home_services_payments
PROVIDERS_DB_NAME=home_services_providers
SCHEDULING_DB_NAME=home_services_scheduling

# Redis Settings (Enable for production)
USE_REDIS=True
REDIS_URL=redis://localhost:6379/1

# Rate Limiting (Enable for production)
RATELIMIT_ENABLE=True

# Security Settings (HTTPS)
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
CSRF_TRUSTED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# JWT Token Settings
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=10080

# MSG91 SMS Gateway Settings
MSG91_AUTH_KEY=your-actual-msg91-auth-key
MSG91_TEMPLATE_ID=your-actual-template-id

# Email Settings
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# CORS Settings
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com,https://www.your-frontend-domain.com

# Payment Gateway (Razorpay)
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
RAZORPAY_WEBHOOK_SECRET=your-razorpay-webhook-secret

# Monitoring & Logging
SENTRY_DSN=your-sentry-dsn-url

# Static Files (if using cloud storage)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
# AWS_S3_REGION_NAME=us-east-1

# Backup Configuration
# BACKUP_ENABLED=True
# BACKUP_S3_BUCKET=your-backup-bucket
