#!/usr/bin/env python3
"""
Production Environment Checker
Verifies that all required environment variables are properly set for production deployment.
"""

import os
import sys
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')

import django
django.setup()

from django.conf import settings
from decouple import config

def check_env_var(var_name, required=True, expected_type=str):
    """Check if environment variable is set and has correct type"""
    value = config(var_name, default=None)
    
    if value is None:
        if required:
            print(f"❌ {var_name}: NOT SET (REQUIRED)")
            return False
        else:
            print(f"⚠️  {var_name}: NOT SET (OPTIONAL)")
            return True
    
    # Type checking
    if expected_type == bool:
        if value.lower() in ['true', '1', 'yes', 'on']:
            print(f"✅ {var_name}: {value} (True)")
        elif value.lower() in ['false', '0', 'no', 'off']:
            print(f"✅ {var_name}: {value} (False)")
        else:
            print(f"❌ {var_name}: {value} (Invalid boolean value)")
            return False
    elif expected_type == int:
        try:
            int_val = int(value)
            print(f"✅ {var_name}: {int_val}")
        except ValueError:
            print(f"❌ {var_name}: {value} (Invalid integer)")
            return False
    else:
        # String type
        if value.strip():
            # Mask sensitive values
            if any(sensitive in var_name.lower() for sensitive in ['key', 'password', 'secret', 'token']):
                masked = value[:4] + '*' * (len(value) - 8) + value[-4:] if len(value) > 8 else '*' * len(value)
                print(f"✅ {var_name}: {masked}")
            else:
                print(f"✅ {var_name}: {value}")
        else:
            print(f"❌ {var_name}: EMPTY")
            return False
    
    return True

def main():
    print("🔍 Production Environment Check")
    print("=" * 50)
    
    all_good = True
    
    # Critical Settings
    print("\n🔧 Critical Settings:")
    all_good &= check_env_var('SECRET_KEY', required=True)
    all_good &= check_env_var('DEBUG', required=True, expected_type=bool)
    all_good &= check_env_var('ALLOWED_HOSTS', required=True)
    
    # Database Settings
    print("\n🗄️  Database Settings:")
    all_good &= check_env_var('DB_NAME', required=True)
    all_good &= check_env_var('DB_USER', required=True)
    all_good &= check_env_var('DB_PASSWORD', required=True)
    all_good &= check_env_var('DB_HOST', required=True)
    all_good &= check_env_var('DB_PORT', required=True)
    
    # Security Settings
    print("\n🔒 Security Settings:")
    all_good &= check_env_var('SECURE_SSL_REDIRECT', required=True, expected_type=bool)
    all_good &= check_env_var('SECURE_HSTS_SECONDS', required=True, expected_type=int)
    all_good &= check_env_var('SECURE_HSTS_INCLUDE_SUBDOMAINS', required=True, expected_type=bool)
    all_good &= check_env_var('SESSION_COOKIE_SECURE', required=True, expected_type=bool)
    all_good &= check_env_var('CSRF_COOKIE_SECURE', required=True, expected_type=bool)
    all_good &= check_env_var('CSRF_TRUSTED_ORIGINS', required=True)
    
    # Redis & Rate Limiting
    print("\n⚡ Redis & Rate Limiting:")
    all_good &= check_env_var('USE_REDIS', required=True, expected_type=bool)
    all_good &= check_env_var('REDIS_URL', required=True)
    all_good &= check_env_var('RATELIMIT_ENABLE', required=True, expected_type=bool)
    
    # External Services
    print("\n📱 External Services:")
    all_good &= check_env_var('MSG91_AUTH_KEY', required=True)
    all_good &= check_env_var('MSG91_TEMPLATE_ID', required=True)
    
    # Optional Settings
    print("\n📧 Optional Settings:")
    check_env_var('EMAIL_HOST_USER', required=False)
    check_env_var('EMAIL_HOST_PASSWORD', required=False)
    check_env_var('RAZORPAY_KEY_ID', required=False)
    check_env_var('RAZORPAY_KEY_SECRET', required=False)
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("✅ All required environment variables are properly configured!")
        print("🚀 Ready for production deployment!")
        return 0
    else:
        print("❌ Some required environment variables are missing or misconfigured.")
        print("📝 Please update your .env file with the missing values.")
        print("💡 Refer to .env.production for the complete list of required variables.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
