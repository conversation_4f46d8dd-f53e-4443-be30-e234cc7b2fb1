# Generated by Django 4.2.7 on 2025-06-23 05:33

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('taxation', '0001_initial'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='gstrate',
            name='taxation_gstrate_tax_category_gst_type_effective_from_uniq',
        ),
        migrations.RenameIndex(
            model_name='taxcalculation',
            new_name='taxation_ta_referen_192388_idx',
            old_name='taxation_ta_referen_b8e5b8_idx',
        ),
        migrations.AlterUniqueTogether(
            name='gstrate',
            unique_together={('tax_category', 'gst_type', 'effective_from')},
        ),
    ]
