-- Production Database Setup Script for Home Services API
-- Run this script as PostgreSQL superuser (postgres)
-- Usage: sudo -u postgres psql -f setup_production_databases.sql

-- Create the application user
CREATE USER home_services_user WITH PASSWORD 'CHANGE_THIS_PASSWORD';

-- Create all databases for microservices architecture
CREATE DATABASE home_services_auth OWNER home_services_user;
CREATE DATABASE home_services_catalogue OWNER home_services_user;
CREATE DATABASE home_services_cart OWNER home_services_user;
CREATE DATABASE home_services_coupons OWNER home_services_user;
CREATE DATABASE home_services_orders OWNER home_services_user;
CREATE DATABASE home_services_payments OWNER home_services_user;
CREATE DATABASE home_services_providers OWNER home_services_user;
CREATE DATABASE home_services_scheduling OWNER home_services_user;

-- Grant all privileges on databases to the user
GRANT ALL PRIVILEGES ON DATABASE home_services_auth TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_catalogue TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_cart TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_coupons TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_orders TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_payments TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_providers TO home_services_user;
GRANT ALL PRIVILEGES ON DATABASE home_services_scheduling TO home_services_user;

-- Grant additional privileges for schema operations
ALTER USER home_services_user CREATEDB;

-- Connect to each database and grant schema privileges
\c home_services_auth
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

\c home_services_catalogue
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

\c home_services_cart
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

\c home_services_coupons
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

\c home_services_orders
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

\c home_services_payments
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

\c home_services_providers
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

\c home_services_scheduling
GRANT ALL ON SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO home_services_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO home_services_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO home_services_user;

-- Return to default database
\c postgres

-- Display created databases
\l

-- Display user information
\du home_services_user

-- Success message
SELECT 'Database setup completed successfully!' as status;
