(venv) homeservices@vmi2555830:~/django_backend$ python manage.py check --deploy
System check identified some issues:

WARNINGS:
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileLoginView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b04190>" for POST /api/auth/login/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileLoginView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b04340>" for POST /api/auth/login/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileLoginView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b04370>" for POST /api/auth/login/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileRegisterView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b059f0>" for POST /api/auth/register/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [MobileRegisterView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b05ae0>" for POST /api/auth/register/mobile/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [ResendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b05210>" for POST /api/auth/otp/resend/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [ResendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b052d0>" for POST /api/auth/otp/resend/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [ResendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b05330>" for POST /api/auth/otp/resend/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b07af0>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b07be0>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b07c10>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [SendOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b07ca0>" for POST /api/auth/otp/send/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [VerifyOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b04d30>" for POST /api/auth/otp/verify/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/authentication/views.py: Warning [VerifyOTPView]: could not resolve "<drf_spectacular.utils.OpenApiExample object at 0x7f7599b04dc0>" for POST /api/auth/otp/verify/. Expected either a serializer or some supported override mechanism. Defaulting to generic free-form object.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer > CartItemSerializer]: unable to resolve type hint for function "get_savings". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer > CartItemSerializer]: unable to resolve type hint for function "get_total_price". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_items_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartDetailView > CartSerializer]: unable to resolve type hint for function "get_unique_services_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartSummaryView > CartSummarySerializer]: unable to resolve type hint for function "get_items_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/cart/serializers.py: Warning [CartSummaryView > CartSummarySerializer]: unable to resolve type hint for function "get_unique_services_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_current_price". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_discount_percentage". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_seo_description". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer > ServiceListSerializer]: unable to resolve type hint for function "get_seo_title". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer]: unable to resolve type hint for function "get_og_image". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer]: unable to resolve type hint for function "get_seo_description". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryDetailView > CategorySerializer]: unable to resolve type hint for function "get_seo_title". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryListCreateView > CategoryListSerializer]: unable to resolve type hint for function "get_seo_description". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [CategoryListCreateView > CategoryListSerializer]: unable to resolve type hint for function "get_seo_title". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [DiscountDetailView > DiscountSerializer]: unable to resolve type hint for function "is_valid". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [DiscountListCreateView > DiscountSerializer]: unable to resolve type hint for function "is_valid". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [ServiceDetailView > ServiceSerializer]: unable to resolve type hint for function "get_current_price". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [ServiceDetailView > ServiceSerializer]: unable to resolve type hint for function "get_discount_percentage". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [ServiceDetailView > ServiceSerializer]: unable to resolve type hint for function "get_og_image". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [ServiceDetailView > ServiceSerializer]: unable to resolve type hint for function "get_seo_description". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/catalogue/serializers.py: Warning [ServiceDetailView > ServiceSerializer]: unable to resolve type hint for function "get_seo_title". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/coupons/serializers.py: Warning [CouponDetailView > CouponSerializer]: unable to resolve type hint for function "get_total_usage". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/coupons/serializers.py: Warning [CouponDetailView > CouponSerializer]: unable to resolve type hint for function "is_valid". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/coupons/serializers.py: Warning [CouponListCreateView > CouponListSerializer]: unable to resolve type hint for function "get_total_usage". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/coupons/serializers.py: Warning [CouponListCreateView > CouponListSerializer]: unable to resolve type hint for function "is_valid". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/coupons/serializers.py: Warning [UsedCouponListView > UsedCouponSerializer]: unable to resolve type hint for function "get_user_info". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/orders/serializers.py: Warning [OrderListCreateView > OrderListSerializer]: unable to resolve type hint for function "get_customer_mobile". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/orders/serializers.py: Warning [OrderListCreateView > OrderListSerializer]: unable to resolve type hint for function "get_customer_name". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/orders/serializers.py: Warning [OrderListCreateView > OrderListSerializer]: unable to resolve type hint for function "get_items_count". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/providers/serializers.py: Warning [ProviderDocumentListView > ProviderDocumentSerializer]: unable to resolve type hint for function "is_expired". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) /home/<USER>/django_backend/scheduling/serializers.py: Warning [SlotBookingListView > SlotBookingSerializer]: unable to resolve type hint for function "get_time_slot_info". Consider using a type hint or @extend_schema_field. Defaulting to string.
?: (drf_spectacular.W001) Warning: enum naming encountered a non-optimally resolvable collision for fields named "status". The same name has been used for multiple choice sets in multiple components. The collision was resolved with "StatusD8cEnum". add an entry to ENUM_NAME_OVERRIDES to fix the naming.
?: (drf_spectacular.W001) Warning: operationId "api_auth_addresses_retrieve" has collisions [('/api/auth/addresses/', 'get'), ('/api/auth/addresses/{id}/', 'get')]. resolving with numeral suffixes.
?: (drf_spectacular.W001) Warning: operationId "api_providers_payouts_retrieve" has collisions [('/api/providers/payouts/', 'get'), ('/api/providers/payouts/{id}/', 'get')]. resolving with numeral suffixes.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminResetRateLimitView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminUnlockAccountView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminUserStatsView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [AdminUsersListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [ChangePasswordView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [HealthCheckView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [LogoutView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/authentication/views.py: Error [UserProfileView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [AdminCartItemListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [AdminCartListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [add_to_cart]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [apply_coupon]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [clear_cart]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [debug_cart_info]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [remove_cart_item]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [remove_coupon]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/cart/views.py: Error [update_cart_item]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/catalogue/views.py: Error [category_services]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/catalogue/views.py: Error [search_services]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/coupons/views.py: Error [AdminCouponListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/coupons/views.py: Error [AdminUsedCouponListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/coupons/views.py: Error [apply_coupon]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/coupons/views.py: Error [apply_sequential_coupons]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/coupons/views.py: Error [redeem_coupon]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/coupons/views.py: Error [validate_coupon]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [OrderDetailView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'AnonymousUser' object has no attribute 'user_type')
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [accept_order]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [assign_provider]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [cancel_order]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [complete_order]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [create_cod_order]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [hold_order]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [mark_incomplete]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [order_dashboard]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [reschedule_order]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [update_order_status]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/orders/views.py: Error [update_payment_status]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [calculate_partial_payment]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [cod_confirm]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [initiate_payment]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [initiate_refund]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [payment_configuration]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [payment_status]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [razorpay_callback]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [razorpay_webhook]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [refund_status]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/payments/views.py: Error [verify_payment]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [AdminPayoutListView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'AdminPayoutListView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [AdminProviderDocumentsView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'AdminProviderDocumentsView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [AdminProviderListView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'AdminProviderListView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [ProviderAvailabilityListView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'ProviderAvailabilityListView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [ProviderBankDetailsView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'ProviderBankDetailsView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [ProviderDocumentDetailView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'ProviderDocumentDetailView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [ProviderPayoutDetailView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'ProviderPayoutDetailView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [ProviderPayoutListView]: exception raised while getting serializer. Hint: Is get_serializer_class() returning None or is get_queryset() not working without a request? Ignoring the view for now. (Exception: 'ProviderPayoutListView' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.)
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [process_payout]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [request_payout]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [update_availability]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [update_bank_details]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [upload_document]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [verify_document]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/providers/views.py: Error [verify_provider]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/scheduling/views.py: Error [AdminSchedulingDashboardView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/scheduling/views.py: Error [AdminSlotBlockageListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/scheduling/views.py: Error [bulk_update_slots]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/scheduling/views.py: Error [create_booking]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/scheduling/views.py: Error [generate_slots]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/scheduling/views.py: Error [get_available_slots]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/taxation/views.py: Error [AdminGSTRateListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/taxation/views.py: Error [AdminTaxCategoryListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (drf_spectacular.W002) /home/<USER>/django_backend/taxation/views.py: Error [AdminTaxConfigurationListView]: unable to guess serializer. This is graceful fallback handling for APIViews. Consider using GenericAPIView as view base class, if view is under your control. Either way you may want to add a serializer_class (or method). Ignoring view for now.
?: (security.W021) You have not set the SECURE_HSTS_PRELOAD setting to True. Without this, your site cannot be submitted to the browser preload list.

System check identified 121 issues (0 silenced).
(venv) homeservices@vmi2555830:~/django_backend$