# 🚨 Production Error Fixes

## Issues Fixed

### ✅ 1. Critical DRF Spectacular Error (FIXED)
**Error**: `drf_spectacular.E001` - Redundant `source='is_valid'` parameter in DiscountSerializer

**Fix Applied**:
- Removed redundant `source='is_valid'` from `DiscountSerializer` in `catalogue/serializers.py`
- Removed redundant `source='is_valid'` from `CouponSerializer` in `coupons/serializers.py`

### ✅ 2. DRF Spectacular Schema Warnings (IMPROVED)
**Warnings**: Multiple `drf_spectacular.W001` warnings about missing type hints

**Fixes Applied**:
- Added `@extend_schema_field` decorators to serializer methods in `cart/serializers.py`
- Added `@extend_schema_field` decorators to serializer methods in `catalogue/serializers.py`
- Added `serializer_class` attributes to APIView classes in `authentication/views.py`

### ⚠️ 3. Security Settings (REQUIRES SERVER CONFIGURATION)
**Warnings**: Multiple security warnings about production settings

**Required Environment Variables** (must be set on production server):
```bash
# Critical Security Settings
DEBUG=False
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
CSRF_TRUSTED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Rate Limiting & Redis
USE_REDIS=True
RATELIMIT_ENABLE=True
REDIS_URL=redis://localhost:6379/1
```

## 🔧 Production Server Setup Steps

### Step 1: Update Environment Variables
Copy the `.env.production` file to `.env` on your production server and update all placeholder values:

```bash
# On production server
cp .env.production .env
nano .env  # Update all placeholder values
```

### Step 2: Verify Environment Configuration
Run the environment checker script:

```bash
python check_production_env.py
```

### Step 3: Run Django Deployment Check
After fixing environment variables, run:

```bash
python manage.py check --deploy
```

### Step 4: Collect Static Files
```bash
python manage.py collectstatic --noinput
```

### Step 5: Run Migrations
```bash
python manage.py migrate
```

## 🔍 Environment Variables Checklist

### Required for Security Warnings Fix:
- [ ] `DEBUG=False`
- [ ] `SECURE_SSL_REDIRECT=True`
- [ ] `SECURE_HSTS_SECONDS=31536000`
- [ ] `SECURE_HSTS_INCLUDE_SUBDOMAINS=True`
- [ ] `SESSION_COOKIE_SECURE=True`
- [ ] `CSRF_COOKIE_SECURE=True`
- [ ] `CSRF_TRUSTED_ORIGINS=https://your-domain.com`

### Required for Basic Operation:
- [ ] `SECRET_KEY=your-production-secret-key`
- [ ] `ALLOWED_HOSTS=your-domain.com,www.your-domain.com`
- [ ] `DB_NAME=home_services_auth`
- [ ] `DB_USER=your-db-user`
- [ ] `DB_PASSWORD=your-db-password`
- [ ] `MSG91_AUTH_KEY=your-msg91-key`
- [ ] `MSG91_TEMPLATE_ID=your-template-id`

### Optional but Recommended:
- [ ] `USE_REDIS=True`
- [ ] `RATELIMIT_ENABLE=True`
- [ ] `EMAIL_HOST_USER=your-email`
- [ ] `EMAIL_HOST_PASSWORD=your-email-password`

## 🚀 Quick Fix Commands

Run these commands on your production server after updating the `.env` file:

```bash
# 1. Check environment configuration
python check_production_env.py

# 2. Run deployment checks
python manage.py check --deploy

# 3. Collect static files
python manage.py collectstatic --noinput

# 4. Restart your web server (example for systemd)
sudo systemctl restart your-django-app
sudo systemctl restart nginx
```

## 📝 Notes

1. **SSL Certificate**: Ensure you have a valid SSL certificate installed before setting `SECURE_SSL_REDIRECT=True`
2. **Redis**: Install and configure Redis server before setting `USE_REDIS=True`
3. **Database**: Ensure all required databases are created and accessible
4. **Firewall**: Configure firewall to allow HTTP/HTTPS traffic

## 🔧 Troubleshooting

If you still get errors after applying these fixes:

1. **Check logs**: `tail -f /var/log/your-app/django.log`
2. **Verify database connections**: `python manage.py dbshell`
3. **Test Redis connection**: `redis-cli ping`
4. **Check file permissions**: Ensure Django can read/write necessary files
5. **Verify SSL certificate**: Use online SSL checkers

## 📞 Support

If you encounter issues:
1. Check the Django logs for specific error messages
2. Verify all environment variables are correctly set
3. Ensure all external services (Redis, PostgreSQL, MSG91) are accessible
4. Test each component individually before running the full application
