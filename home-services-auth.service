# Systemd service file for Home Services API
# Copy this to /etc/systemd/system/home-services-api.service

[Unit]
Description=Home Services API
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=notify
User=homeservices
Group=homeservices
RuntimeDirectory=gunicorn
WorkingDirectory=/home/<USER>/django_backend
Environment=PATH=/home/<USER>/django_backend/venv/bin
ExecStart=/home/<USER>/django_backend/venv/bin/gunicorn --config gunicorn.conf.py home_services.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=5

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/home/<USER>/django_backend/logs /home/<USER>/django_backend/media /var/log/gunicorn

# Environment variables (alternatively, use EnvironmentFile)
Environment=DJANGO_SETTINGS_MODULE=home_services.settings

[Install]
WantedBy=multi-user.target
